{"version": 3, "sources": ["../../@dnd-kit/utilities/src/hooks/useCombinedRefs.ts", "../../@dnd-kit/utilities/src/execution-context/canUseDOM.ts", "../../@dnd-kit/utilities/src/type-guards/isWindow.ts", "../../@dnd-kit/utilities/src/type-guards/isNode.ts", "../../@dnd-kit/utilities/src/execution-context/getWindow.ts", "../../@dnd-kit/utilities/src/type-guards/isDocument.ts", "../../@dnd-kit/utilities/src/type-guards/isHTMLElement.ts", "../../@dnd-kit/utilities/src/type-guards/isSVGElement.ts", "../../@dnd-kit/utilities/src/execution-context/getOwnerDocument.ts", "../../@dnd-kit/utilities/src/hooks/useIsomorphicLayoutEffect.ts", "../../@dnd-kit/utilities/src/hooks/useEvent.ts", "../../@dnd-kit/utilities/src/hooks/useInterval.ts", "../../@dnd-kit/utilities/src/hooks/useLatestValue.ts", "../../@dnd-kit/utilities/src/hooks/useLazyMemo.ts", "../../@dnd-kit/utilities/src/hooks/useNodeRef.ts", "../../@dnd-kit/utilities/src/hooks/usePrevious.ts", "../../@dnd-kit/utilities/src/hooks/useUniqueId.ts", "../../@dnd-kit/utilities/src/adjustment.ts", "../../@dnd-kit/utilities/src/event/hasViewportRelativeCoordinates.ts", "../../@dnd-kit/utilities/src/event/isKeyboardEvent.ts", "../../@dnd-kit/utilities/src/event/isTouchEvent.ts", "../../@dnd-kit/utilities/src/coordinates/getEventCoordinates.ts", "../../@dnd-kit/utilities/src/css.ts", "../../@dnd-kit/utilities/src/focus/findFirstFocusableNode.ts"], "sourcesContent": ["import {useMemo} from 'react';\n\nexport function useCombinedRefs<T>(\n  ...refs: ((node: T) => void)[]\n): (node: T) => void {\n  return useMemo(\n    () => (node: T) => {\n      refs.forEach((ref) => ref(node));\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n", "// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow';\nimport {isNode} from '../type-guards/isNode';\n\nexport function getWindow(target: Event['target']): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return node instanceof Document;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nimport {isWindow} from './isWindow';\n\nexport function isHTMLElement(node: Node | Window): node is HTMLElement {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return node instanceof getWindow(node).SVGElement;\n}\n", "import {\n  isWindow,\n  isHTMLElement,\n  isDocument,\n  isNode,\n  isSVGElement,\n} from '../type-guards';\n\nexport function getOwnerDocument(target: Event['target']): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import {useEffect, useLayoutEffect} from 'react';\n\nimport {canUseDOM} from '../execution-context';\n\n/**\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\n */\nexport const useIsomorphicLayoutEffect = canUseDOM\n  ? useLayoutEffect\n  : useEffect;\n", "import {useCallback, useRef} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useEvent<T extends Function>(handler: T | undefined) {\n  const handlerRef = useRef<T | undefined>(handler);\n\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n\n  return useCallback(function (...args: any) {\n    return handlerRef.current?.(...args);\n  }, []);\n}\n", "import {useCallback, useRef} from 'react';\n\nexport function useInterval() {\n  const intervalRef = useRef<number | null>(null);\n\n  const set = useCallback((listener: Function, duration: number) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  return [set, clear] as const;\n}\n", "import {useRef} from 'react';\nimport type {DependencyList} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useLatestValue<T extends any>(\n  value: T,\n  dependencies: DependencyList = [value]\n) {\n  const valueRef = useRef<T>(value);\n\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n\n  return valueRef;\n}\n", "import {useMemo, useRef} from 'react';\n\nexport function useLazyMemo<T>(\n  callback: (prevValue: T | undefined) => T,\n  dependencies: any[]\n) {\n  const valueRef = useRef<T>();\n\n  return useMemo(\n    () => {\n      const newValue = callback(valueRef.current);\n      valueRef.current = newValue;\n\n      return newValue;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...dependencies]\n  );\n}\n", "import {useRef, useCallback} from 'react';\n\nimport {useEvent} from './useEvent';\n\nexport function useNodeRef(\n  onChange?: (\n    newElement: HTMLElement | null,\n    previousElement: HTMLElement | null\n  ) => void\n) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef<HTMLElement | null>(null);\n  const setNodeRef = useCallback(\n    (element: HTMLElement | null) => {\n      if (element !== node.current) {\n        onChangeHandler?.(element, node.current);\n      }\n\n      node.current = element;\n    },\n    //eslint-disable-next-line\n    []\n  );\n\n  return [node, setNodeRef] as const;\n}\n", "import {useRef, useEffect} from 'react';\n\nexport function usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n", "import {useMemo} from 'react';\n\nlet ids: Record<string, number> = {};\n\nexport function useUniqueId(prefix: string, value?: string) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n\n    return `${prefix}-${id}`;\n  }, [prefix, value]);\n}\n", "function createAdjustmentFn(modifier: number) {\n  return <T extends Record<U, number>, U extends string>(\n    object: T,\n    ...adjustments: Partial<T>[]\n  ): T => {\n    return adjustments.reduce<T>(\n      (accumulator, adjustment) => {\n        const entries = Object.entries(adjustment) as [U, number][];\n\n        for (const [key, valueAdjustment] of entries) {\n          const value = accumulator[key];\n\n          if (value != null) {\n            accumulator[key] = (value + modifier * valueAdjustment) as T[U];\n          }\n        }\n\n        return accumulator;\n      },\n      {\n        ...object,\n      }\n    );\n  };\n}\n\nexport const add = createAdjustmentFn(1);\nexport const subtract = createAdjustmentFn(-1);\n", "export function hasViewportRelativeCoordinates(\n  event: Event\n): event is Event & Pick<PointerEvent, 'clientX' | 'clientY'> {\n  return 'clientX' in event && 'clientY' in event;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isKeyboardEvent(\n  event: Event | undefined | null\n): event is KeyboardEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isTouchEvent(\n  event: Event | undefined | null\n): event is TouchEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {TouchEvent} = getWindow(event.target);\n\n  return TouchEvent && event instanceof TouchEvent;\n}\n", "import type {Coordinates} from './types';\nimport {isTouchEvent, hasViewportRelativeCoordinates} from '../event';\n\n/**\n * Returns the normalized x and y coordinates for mouse and touch events.\n */\nexport function getEventCoordinates(event: Event): Coordinates | null {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {clientX: x, clientY: y} = event.touches[0];\n\n      return {\n        x,\n        y,\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {clientX: x, clientY: y} = event.changedTouches[0];\n\n      return {\n        x,\n        y,\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  }\n\n  return null;\n}\n", "export type Transform = {\n  x: number;\n  y: number;\n  scaleX: number;\n  scaleY: number;\n};\n\nexport interface Transition {\n  property: string;\n  easing: string;\n  duration: number;\n}\n\nexport const CSS = Object.freeze({\n  Translate: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {x, y} = transform;\n\n      return `translate3d(${x ? Math.round(x) : 0}px, ${\n        y ? Math.round(y) : 0\n      }px, 0)`;\n    },\n  },\n  Scale: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {scaleX, scaleY} = transform;\n\n      return `scaleX(${scaleX}) scaleY(${scaleY})`;\n    },\n  },\n  Transform: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      return [\n        CSS.Translate.toString(transform),\n        CSS.Scale.toString(transform),\n      ].join(' ');\n    },\n  },\n  Transition: {\n    toString({property, duration, easing}: Transition) {\n      return `${property} ${duration}ms ${easing}`;\n    },\n  },\n});\n", "const SELECTOR =\n  'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\n\nexport function findFirstFocusableNode(\n  element: HTMLElement\n): HTMLElement | null {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n"], "mappings": ";;;;;;;SAEgBA,kBAAAA;oCACXC,OAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,SAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAEH,aAAOC;IACL,MAAOC,UAAD;AACJF,WAAKG,QAASC,SAAQA,IAAIF,IAAD,CAAzB;;;IAGFF;EALY;AAOf;ACXD,IAAaK,YACX,OAAOC,WAAW,eAClB,OAAOA,OAAOC,aAAa,eAC3B,OAAOD,OAAOC,SAASC,kBAAkB;SCJ3BC,SAASC,SAAAA;AACvB,QAAMC,gBAAgBC,OAAOC,UAAUC,SAASC,KAAKL,OAA/B;AACtB,SACEC,kBAAkB;EAElBA,kBAAkB;AAErB;SCPeK,OAAOd,MAAAA;AACrB,SAAO,cAAcA;AACtB;SCCee,UAAUC,QAAAA;;AACxB,MAAI,CAACA,QAAQ;AACX,WAAOZ;;AAGT,MAAIG,SAASS,MAAD,GAAU;AACpB,WAAOA;;AAGT,MAAI,CAACF,OAAOE,MAAD,GAAU;AACnB,WAAOZ;;AAGT,UAAA,yBAAA,yBAAOY,OAAOC,kBAAd,OAAA,SAAO,uBAAsBC,gBAA7B,OAAA,wBAA4Cd;AAC7C;SCfee,WAAWnB,MAAAA;AACzB,QAAM;IAACoB;MAAYL,UAAUf,IAAD;AAE5B,SAAOA,gBAAgBoB;AACxB;SCFeC,cAAcrB,MAAAA;AAC5B,MAAIO,SAASP,IAAD,GAAQ;AAClB,WAAO;;AAGT,SAAOA,gBAAgBe,UAAUf,IAAD,EAAOsB;AACxC;SCReC,aAAavB,MAAAA;AAC3B,SAAOA,gBAAgBe,UAAUf,IAAD,EAAOwB;AACxC;SCIeC,iBAAiBT,QAAAA;AAC/B,MAAI,CAACA,QAAQ;AACX,WAAOX;;AAGT,MAAIE,SAASS,MAAD,GAAU;AACpB,WAAOA,OAAOX;;AAGhB,MAAI,CAACS,OAAOE,MAAD,GAAU;AACnB,WAAOX;;AAGT,MAAIc,WAAWH,MAAD,GAAU;AACtB,WAAOA;;AAGT,MAAIK,cAAcL,MAAD,KAAYO,aAAaP,MAAD,GAAU;AACjD,WAAOA,OAAOC;;AAGhB,SAAOZ;AACR;ACtBD,IAAaqB,4BAA4BvB,YACrCwB,+BACAC;SCNYC,SAA6BC,SAAAA;AAC3C,QAAMC,iBAAaC,qBAAsBF,OAAhB;AAEzBJ,4BAA0B,MAAA;AACxBK,eAAWE,UAAUH;GADE;AAIzB,aAAOI,0BAAY,WAAA;sCAAaC,OAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,WAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAC9B,WAAOJ,WAAWE,WAAlB,OAAA,SAAOF,WAAWE,QAAU,GAAGE,IAAxB;KACN,CAAA,CAFe;AAGnB;SCZeC,cAAAA;AACd,QAAMC,kBAAcL,qBAAsB,IAAhB;AAE1B,QAAMM,UAAMJ,0BAAY,CAACK,UAAoBC,aAArB;AACtBH,gBAAYJ,UAAUQ,YAAYF,UAAUC,QAAX;KAChC,CAAA,CAFoB;AAIvB,QAAME,YAAQR,0BAAY,MAAA;AACxB,QAAIG,YAAYJ,YAAY,MAAM;AAChCU,oBAAcN,YAAYJ,OAAb;AACbI,kBAAYJ,UAAU;;KAEvB,CAAA,CALsB;AAOzB,SAAO,CAACK,KAAKI,KAAN;AACR;SCZeE,eACdC,OACAC,cAAAA;MAAAA,iBAAAA,QAAAA;AAAAA,mBAA+B,CAACD,KAAD;;AAE/B,QAAME,eAAWf,qBAAUa,KAAJ;AAEvBnB,4BAA0B,MAAA;AACxB,QAAIqB,SAASd,YAAYY,OAAO;AAC9BE,eAASd,UAAUY;;KAEpBC,YAJsB;AAMzB,SAAOC;AACR;SChBeC,YACdC,UACAH,cAAAA;AAEA,QAAMC,eAAWf,qBAAM;AAEvB,aAAOjC;IACL,MAAA;AACE,YAAMmD,WAAWD,SAASF,SAASd,OAAV;AACzBc,eAASd,UAAUiB;AAEnB,aAAOA;;;IAGT,CAAC,GAAGJ,YAAJ;EARY;AAUf;SCdeK,WACdC,UAAAA;AAKA,QAAMC,kBAAkBxB,SAASuB,QAAD;AAChC,QAAMpD,WAAOgC,qBAA2B,IAArB;AACnB,QAAMsB,iBAAapB;IAChB1B,aAAD;AACE,UAAIA,YAAYR,KAAKiC,SAAS;AAC5BoB,2BAAe,OAAf,SAAAA,gBAAkB7C,SAASR,KAAKiC,OAAjB;;AAGjBjC,WAAKiC,UAAUzB;;;IAGjB,CAAA;EAT4B;AAY9B,SAAO,CAACR,MAAMsD,UAAP;AACR;SCvBeC,YAAeV,OAAAA;AAC7B,QAAM3C,UAAM8B,qBAAM;AAElBJ,8BAAU,MAAA;AACR1B,QAAI+B,UAAUY;KACb,CAACA,KAAD,CAFM;AAIT,SAAO3C,IAAI+B;AACZ;ACRD,IAAIuB,MAA8B,CAAA;AAElC,SAAgBC,YAAYC,QAAgBb,OAAAA;AAC1C,aAAO9C,sBAAQ,MAAA;AACb,QAAI8C,OAAO;AACT,aAAOA;;AAGT,UAAMc,KAAKH,IAAIE,MAAD,KAAY,OAAO,IAAIF,IAAIE,MAAD,IAAW;AACnDF,QAAIE,MAAD,IAAWC;AAEd,WAAUD,SAAV,MAAoBC;KACnB,CAACD,QAAQb,KAAT,CATW;AAUf;ACfD,SAASe,mBAAmBC,UAA5B;AACE,SAAO,SACLC,QADK;sCAEFC,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,kBAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AAEH,WAAOA,YAAYC,OACjB,CAACC,aAAaC,eAAd;AACE,YAAMC,UAAUzD,OAAOyD,QAAQD,UAAf;AAEhB,iBAAW,CAACE,KAAKC,eAAN,KAA0BF,SAAS;AAC5C,cAAMtB,QAAQoB,YAAYG,GAAD;AAEzB,YAAIvB,SAAS,MAAM;AACjBoB,sBAAYG,GAAD,IAASvB,QAAQgB,WAAWQ;;;AAI3C,aAAOJ;OAET;MACE,GAAGH;KAfA;;AAmBV;AAED,IAAaQ,MAAMV,mBAAmB,CAAD;AACrC,IAAaW,WAAWX,mBAAmB,EAAD;SC3B1BY,+BACdC,OAAAA;AAEA,SAAO,aAAaA,SAAS,aAAaA;AAC3C;SCFeC,gBACdD,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAM;IAACE;MAAiB5D,UAAU0D,MAAMzD,MAAP;AAEjC,SAAO2D,iBAAiBF,iBAAiBE;AAC1C;SCVeC,aACdH,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAM;IAACI;MAAc9D,UAAU0D,MAAMzD,MAAP;AAE9B,SAAO6D,cAAcJ,iBAAiBI;AACvC;ACND,SAAgBC,oBAAoBL,OAAAA;AAClC,MAAIG,aAAaH,KAAD,GAAS;AACvB,QAAIA,MAAMM,WAAWN,MAAMM,QAAQC,QAAQ;AACzC,YAAM;QAACC,SAASC;QAAGC,SAASC;UAAKX,MAAMM,QAAQ,CAAd;AAEjC,aAAO;QACLG;QACAE;;eAEOX,MAAMY,kBAAkBZ,MAAMY,eAAeL,QAAQ;AAC9D,YAAM;QAACC,SAASC;QAAGC,SAASC;UAAKX,MAAMY,eAAe,CAArB;AAEjC,aAAO;QACLH;QACAE;;;;AAKN,MAAIZ,+BAA+BC,KAAD,GAAS;AACzC,WAAO;MACLS,GAAGT,MAAMQ;MACTG,GAAGX,MAAMU;;;AAIb,SAAO;AACR;ICpBYG,MAAM5E,OAAO6E,OAAO;EAC/BC,WAAW;IACT5E,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,YAAM;QAACP;QAAGE;UAAKK;AAEf,aAAA,kBAAsBP,IAAIQ,KAAKC,MAAMT,CAAX,IAAgB,KAA1C,UACEE,IAAIM,KAAKC,MAAMP,CAAX,IAAgB,KADtB;;;EAKJQ,OAAO;IACLhF,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,YAAM;QAACI;QAAQC;UAAUL;AAEzB,aAAA,YAAiBI,SAAjB,cAAmCC,SAAnC;;;EAGJC,WAAW;IACTnF,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,aAAO,CACLH,IAAIE,UAAU5E,SAAS6E,SAAvB,GACAH,IAAIM,MAAMhF,SAAS6E,SAAnB,CAFK,EAGLO,KAAK,GAHA;;;EAMXC,YAAY;IACVrF,SAAQ,MAAA;UAAC;QAACsF;QAAU1D;QAAU2D;;AAC5B,aAAUD,WAAV,MAAsB1D,WAAtB,QAAoC2D;;;AAvCT,CAAd;ACbnB,IAAMC,WACJ;AAEF,SAAgBC,uBACd7F,SAAAA;AAEA,MAAIA,QAAQ8F,QAAQF,QAAhB,GAA2B;AAC7B,WAAO5F;;AAGT,SAAOA,QAAQ+F,cAAcH,QAAtB;AACR;", "names": ["useCombinedRefs", "refs", "useMemo", "node", "for<PERSON>ach", "ref", "canUseDOM", "window", "document", "createElement", "isWindow", "element", "elementString", "Object", "prototype", "toString", "call", "isNode", "getWindow", "target", "ownerDocument", "defaultView", "isDocument", "Document", "isHTMLElement", "HTMLElement", "isSVGElement", "SVGElement", "getOwnerDocument", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "current", "useCallback", "args", "useInterval", "intervalRef", "set", "listener", "duration", "setInterval", "clear", "clearInterval", "useLatestValue", "value", "dependencies", "valueRef", "useLazyMemo", "callback", "newValue", "useNodeRef", "onChange", "onChangeHandler", "setNodeRef", "usePrevious", "ids", "useUniqueId", "prefix", "id", "createAdjustmentFn", "modifier", "object", "adjustments", "reduce", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isKeyboardEvent", "KeyboardEvent", "isTouchEvent", "TouchEvent", "getEventCoordinates", "touches", "length", "clientX", "x", "clientY", "y", "changedTouches", "CSS", "freeze", "Translate", "transform", "Math", "round", "Scale", "scaleX", "scaleY", "Transform", "join", "Transition", "property", "easing", "SELECTOR", "findFirstFocusableNode", "matches", "querySelector"]}