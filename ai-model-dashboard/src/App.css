/* Cyber Tech Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px #00d4ff, 0 0 10px #00d4ff, 0 0 15px #00d4ff; }
  50% { box-shadow: 0 0 10px #00d4ff, 0 0 20px #00d4ff, 0 0 30px #00d4ff; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes neon-text {
  0%, 100% { text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor; }
  50% { text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor; }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-neon-text {
  animation: neon-text 2s ease-in-out infinite;
}

.bg-cyber-orb-1 {
  background: rgba(99, 102, 241, 0.1);
  animation-delay: 0s;
}

.bg-cyber-orb-2 {
  background: rgba(168, 85, 247, 0.1);
  animation-delay: 1s;
}

.bg-cyber-orb-3 {
  background: rgba(34, 197, 94, 0.05);
  animation-delay: 2s;
}

/* Cyber Tech Styles */
.cyber-border {
  position: relative;
  border: 1px solid #00d4ff;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
}

.cyber-border::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #00d4ff, #a855f7, #00ff88, #ff6b35);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cyber-border:hover::before {
  opacity: 1;
}

.cyber-card {
  background: rgba(26, 26, 46, 0.9);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cyber-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.cyber-card:hover::before {
  left: 100%;
}

.cyber-card:hover {
  border-color: #00d4ff;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  transform: translateY(-2px);
}

.cyber-button {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(168, 85, 247, 0.1));
  border: 1px solid #00d4ff;
  color: #00d4ff;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cyber-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.2) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.cyber-button:hover::before {
  width: 300px;
  height: 300px;
}

.cyber-button:hover {
  color: #ffffff;
  border-color: #00ff88;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
  text-shadow: 0 0 10px currentColor;
}

.cyber-button.primary {
  background: linear-gradient(135deg, #00d4ff, #a855f7);
  border-color: #00d4ff;
  color: white;
}

.cyber-button.success {
  background: linear-gradient(135deg, #00ff88, #00d4ff);
  border-color: #00ff88;
  color: white;
}

.cyber-button.danger {
  background: linear-gradient(135deg, #ff0080, #ff6b35);
  border-color: #ff0080;
  color: white;
}

.cyber-input {
  background: rgba(10, 10, 15, 0.8);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #e1e5e9;
  font-family: 'JetBrains Mono', monospace;
  transition: all 0.3s ease;
}

.cyber-input:focus {
  border-color: #00d4ff;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  outline: none;
}

.cyber-text-primary {
  color: #00d4ff;
  text-shadow: 0 0 5px currentColor;
}

.cyber-text-secondary {
  color: #a855f7;
  text-shadow: 0 0 5px currentColor;
}

.cyber-text-success {
  color: #00ff88;
  text-shadow: 0 0 5px currentColor;
}

.cyber-text-warning {
  color: #ff6b35;
  text-shadow: 0 0 5px currentColor;
}

.cyber-text-danger {
  color: #ff0080;
  text-shadow: 0 0 5px currentColor;
}

/* Rank styling */
.rank-badge {
  background: linear-gradient(135deg, #00d4ff, #a855f7);
  color: white;
  font-weight: 700;
  text-shadow: 0 0 5px currentColor;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.rank-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.rank-badge:hover::before {
  left: 100%;
}

.rank-1 { 
  background: linear-gradient(135deg, #ffd700, #ff6b35); 
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}
.rank-2 { 
  background: linear-gradient(135deg, #c0c0c0, #00d4ff); 
  box-shadow: 0 0 15px rgba(192, 192, 192, 0.5);
}
.rank-3 { 
  background: linear-gradient(135deg, #cd7f32, #a855f7); 
  box-shadow: 0 0 15px rgba(205, 127, 50, 0.5);
}

/* Drag and drop styles */
.sortable-ghost {
  opacity: 0.4;
  transform: scale(0.95);
}

.sortable-drag {
  opacity: 0.8;
  transform: rotate(2deg) scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 26, 46, 0.8);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00d4ff, #a855f7);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #00ff88, #00d4ff);
}

/* Focus styles */
button:focus-visible,
input:focus-visible {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}