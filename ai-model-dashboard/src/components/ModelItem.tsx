import React, { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, Trash2, Edit3 } from 'lucide-react';
import { Model } from '../App';

interface ModelItemProps {
  model: Model;
  rank: number;
  scenarioId: string;
  onUpdateModelName: (scenarioId: string, modelId: string, newName: string) => void;
  onDeleteModel: (scenarioId: string, modelId: string) => void;
  isActive: boolean;
}

const ModelItem: React.FC<ModelItemProps> = ({
  model,
  rank,
  scenarioId,
  onUpdateModelName,
  onDeleteModel,
  isActive
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [nameValue, setNameValue] = useState(model.name);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: model.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleNameSubmit = () => {
    if (nameValue.trim() !== model.name) {
      onUpdateModelName(scenarioId, model.id, nameValue.trim());
    }
    setIsEditing(false);
  };

  const handleNameKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNameSubmit();
    } else if (e.key === 'Escape') {
      setNameValue(model.name);
      setIsEditing(false);
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'rank-1'; // Gold gradient
      case 2:
        return 'rank-2'; // Silver gradient
      case 3:
        return 'rank-3'; // Bronze gradient
      default:
        return 'rank-badge';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        flex flex-col items-center cyber-card p-3 transition-all duration-200 group relative overflow-hidden w-24
        ${isDragging ? 'opacity-50 shadow-lg scale-105 animate-glow' : ''}
        ${isActive ? 'ring-2 ring-blue-400' : ''}
        hover:border-blue-400/50
      `}
    >
      {/* Cyber scan line effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-400/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-blue-400 transition-colors duration-200 relative z-10 mb-2"
      >
        <GripVertical size={14} />
      </div>

      {/* Model Name */}
      <div className="flex flex-col items-center gap-2 relative z-10 w-full">
        {isEditing ? (
          <input
            type="text"
            value={nameValue}
            onChange={(e) => setNameValue(e.target.value)}
            onBlur={handleNameSubmit}
            onKeyDown={handleNameKeyPress}
            className="cyber-input px-2 py-1 rounded-md border-2 focus:outline-none text-white font-mono text-xs w-full text-center"
            placeholder="模型名称"
            autoFocus
          />
        ) : (
          <>
            <span
              className="text-gray-200 font-medium cursor-pointer hover:text-blue-300 transition-colors duration-200 font-mono tracking-wide text-xs text-center"
              onClick={() => setIsEditing(true)}
            >
              {model.name}
            </span>
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <Edit3
                size={12}
                className="text-gray-400 hover:text-blue-400 cursor-pointer transition-colors duration-200"
                onClick={() => setIsEditing(true)}
              />
              <button
                type="button"
                onClick={() => onDeleteModel(scenarioId, model.id)}
                className="text-red-400 hover:text-red-300 hover:bg-red-900/20 p-1 rounded-md transition-all duration-200"
                title="删除模型"
              >
                <Trash2 size={12} />
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ModelItem;