import React, { useState } from 'react';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Trash2, Plus, Edit3 } from 'lucide-react';
import ModelItem from './ModelItem';
import { Scenario } from '../App';

interface ScenarioCardProps {
  scenario: Scenario;
  onUpdateScenarioName: (scenarioId: string, newName: string) => void;
  onDeleteScenario: (scenarioId: string) => void;
  onUpdateModelName: (scenarioId: string, modelId: string, newName: string) => void;
  onAddModel: (scenarioId: string) => void;
  onDeleteModel: (scenarioId: string, modelId: string) => void;
  activeId: string | null;
}

const ScenarioCard: React.FC<ScenarioCardProps> = ({
  scenario,
  onUpdateScenarioName,
  onDeleteScenario,
  onUpdateModelName,
  onAddModel,
  onDeleteModel,
  activeId
}) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [titleValue, setTitleValue] = useState(scenario.name);

  const handleTitleSubmit = () => {
    if (titleValue.trim() !== scenario.name) {
      onUpdateScenarioName(scenario.id, titleValue.trim());
    }
    setIsEditingTitle(false);
  };

  const handleTitleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSubmit();
    } else if (e.key === 'Escape') {
      setTitleValue(scenario.name);
      setIsEditingTitle(false);
    }
  };

  // Add new model to scenario (limit to 3)
  const handleAddModel = () => {
    if (scenario.models.length >= 3) {
      return; // Don't add if already has 3 models
    }
    onAddModel(scenario.id);
  };

  return (
    <div className="cyber-card p-8 animate-fade-in relative w-full max-w-4xl">
      {/* Card Header */}
      <div className="flex items-center justify-between mb-8 pb-6 border-b border-blue-500/20">
        <div className="flex items-center gap-3 flex-1">
          {isEditingTitle ? (
            <input
              type="text"
              value={titleValue}
              onChange={(e) => setTitleValue(e.target.value)}
              onBlur={handleTitleSubmit}
              onKeyDown={handleTitleKeyPress}
              className="cyber-input text-2xl font-bold text-white bg-gray-900/80 px-4 py-3 rounded-lg border-2 focus:outline-none flex-1 font-mono"
              placeholder="输入场景名称"
              autoFocus
            />
          ) : (
            <div className="flex items-center gap-3 flex-1">
              <h3 
                className="text-2xl font-bold cyber-text-primary cursor-pointer hover:text-blue-300 transition-colors duration-200 flex-1 font-mono uppercase tracking-wide"
                onClick={() => setIsEditingTitle(true)}
              >
                [ {scenario.name} ]
              </h3>
              <Edit3 
                size={20} 
                className="text-gray-400 hover:text-blue-400 cursor-pointer transition-colors duration-200"
                onClick={() => setIsEditingTitle(true)}
              />
            </div>
          )}
        </div>
        
        <button
          onClick={() => onDeleteScenario(scenario.id)}
          className="text-red-400 hover:text-red-300 hover:bg-red-900/20 p-3 rounded-lg transition-all duration-200"
          title="删除场景"
        >
          <Trash2 size={20} />
        </button>
      </div>

      {/* Horizontal Models Layout */}
      <div className="flex items-center justify-center gap-8 mb-8">
        <SortableContext items={scenario.models.map(m => m.id)} strategy={verticalListSortingStrategy}>
          {scenario.models.map((model, index) => (
            <div key={model.id} className="flex flex-col items-center">
              {/* Rank Circle */}
              <div className={`
                rank-badge mb-4 w-16 h-16 text-xl font-bold
                ${index === 0 ? 'rank-1' : index === 1 ? 'rank-2' : 'rank-3'}
              `}>
                {index + 1}
              </div>
              
              {/* Model Card */}
              <ModelItem
                model={model}
                rank={index + 1}
                scenarioId={scenario.id}
                onUpdateModelName={onUpdateModelName}
                onDeleteModel={onDeleteModel}
                isActive={activeId === model.id}
              />
            </div>
          ))}
        </SortableContext>
      </div>

      {/* Add Model Button - Only show if less than 3 models */}
      {scenario.models.length < 3 && (
        <button
          onClick={handleAddModel}
          className="cyber-button w-full flex items-center justify-center gap-2 py-4 px-6 rounded-lg font-medium border-2 border-dashed border-blue-500/30 hover:border-blue-400/50"
        >
          <Plus size={20} />
          <span className="font-mono">ADD_MODEL</span>
        </button>
      )}
    </div>
  );
};

export default ScenarioCard;