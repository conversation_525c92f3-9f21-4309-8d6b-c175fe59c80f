import React, { useState, useEffect } from 'react';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import ScenarioCard from './components/ScenarioCard';
import { Plus, Save, RotateCcw } from 'lucide-react';
import './App.css';

export interface Model {
  id: string;
  name: string;
}

export interface Scenario {
  id: string;
  name: string;
  models: Model[];
}

const initialScenarios: Scenario[] = [
  {
    id: '1',
    name: '学习知识点',
    models: [
      { id: '1-1', name: 'GPT-4' },
      { id: '1-2', name: '<PERSON>-3' },
      { id: '1-3', name: 'Gemini Pro' }
    ]
  },
  {
    id: '2',
    name: '图表绘制',
    models: [
      { id: '2-1', name: 'GPT-4' },
      { id: '2-2', name: '<PERSON><PERSON><PERSON>' },
      { id: '2-3', name: '<PERSON> <PERSON>' }
    ]
  },
  {
    id: '3',
    name: '智能技术问答',
    models: [
      { id: '3-1', name: 'GPT-4' },
      { id: '3-2', name: 'Claude-3' },
      { id: '3-3', name: 'Gemini Pro' }
    ]
  },
  {
    id: '4',
    name: '代码补全',
    models: [
      { id: '4-1', name: 'GPT-4' },
      { id: '4-2', name: 'Claude-3' },
      { id: '4-3', name: 'Copilot' }
    ]
  },
  {
    id: '5',
    name: '项目理解',
    models: [
      { id: '5-1', name: 'GPT-4' },
      { id: '5-2', name: 'Claude-3' },
      { id: '5-3', name: 'Gemini Pro' }
    ]
  }
];

function App() {
  const [scenarios, setScenarios] = useState<Scenario[]>(initialScenarios);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [activeScenarioId, setActiveScenarioId] = useState<string | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedData = localStorage.getItem('ai-model-scenarios');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setScenarios(parsedData);
      } catch (error) {
        console.error('Error loading saved data:', error);
      }
    }
  }, []);

  // Save data to localStorage whenever scenarios change
  const saveData = () => {
    localStorage.setItem('ai-model-scenarios', JSON.stringify(scenarios));
    alert('数据已保存！');
  };

  // Reset data to initial state
  const resetData = () => {
    if (confirm('确定要重置所有数据吗？这将清除所有自定义更改。')) {
      setScenarios(initialScenarios);
      localStorage.removeItem('ai-model-scenarios');
    }
  };

  // Add new scenario
  const addScenario = () => {
    const newId = Date.now().toString();
    const newScenario: Scenario = {
      id: newId,
      name: '新测试场景',
      models: [
        { id: `${newId}-1`, name: '模型1' },
        { id: `${newId}-2`, name: '模型2' },
        { id: `${newId}-3`, name: '模型3' }
      ]
    };
    setScenarios([...scenarios, newScenario]);
  };

  // Update scenario name
  const updateScenarioName = (scenarioId: string, newName: string) => {
    setScenarios(scenarios.map(scenario =>
      scenario.id === scenarioId
        ? { ...scenario, name: newName }
        : scenario
    ));
  };

  // Delete scenario
  const deleteScenario = (scenarioId: string) => {
    if (confirm('确定要删除这个测试场景吗？')) {
      setScenarios(scenarios.filter(scenario => scenario.id !== scenarioId));
    }
  };

  // Update model name
  const updateModelName = (scenarioId: string, modelId: string, newName: string) => {
    setScenarios(scenarios.map(scenario =>
      scenario.id === scenarioId
        ? {
            ...scenario,
            models: scenario.models.map(model =>
              model.id === modelId ? { ...model, name: newName } : model
            )
          }
        : scenario
    ));
  };

  // Add new model to scenario
  const addModel = (scenarioId: string) => {
    const scenario = scenarios.find(s => s.id === scenarioId);
    if (scenario) {
      const newModelId = `${scenarioId}-${Date.now()}`;
      const newModel: Model = {
        id: newModelId,
        name: `新模型${scenario.models.length + 1}`
      };
      
      setScenarios(scenarios.map(s =>
        s.id === scenarioId
          ? { ...s, models: [...s.models, newModel] }
          : s
      ));
    }
  };

  // Delete model from scenario
  const deleteModel = (scenarioId: string, modelId: string) => {
    setScenarios(scenarios.map(scenario =>
      scenario.id === scenarioId
        ? {
            ...scenario,
            models: scenario.models.filter(model => model.id !== modelId)
          }
        : scenario
    ));
  };

  // Handle drag start
  const handleDragStart = (event: any) => {
    setActiveId(event.active.id);
    // Find which scenario this model belongs to
    const scenarioId = scenarios.find(scenario =>
      scenario.models.some(model => model.id === event.active.id)
    )?.id;
    setActiveScenarioId(scenarioId || null);
  };

  // Handle drag end
  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over?.id && activeScenarioId) {
      const scenario = scenarios.find(s => s.id === activeScenarioId);
      if (scenario) {
        const oldIndex = scenario.models.findIndex(model => model.id === active.id);
        const newIndex = scenario.models.findIndex(model => model.id === over.id);

        const newModels = arrayMove(scenario.models, oldIndex, newIndex);
        
        setScenarios(scenarios.map(s =>
          s.id === activeScenarioId
            ? { ...s, models: newModels }
            : s
        ));
      }
    }

    setActiveId(null);
    setActiveScenarioId(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative">
      {/* Cyber tech background effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyber-orb-1 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyber-orb-2 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyber-orb-3 rounded-full blur-3xl animate-pulse-slow"></div>
      </div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Header */}
        <header className="text-center mb-12">
          <div className="inline-block p-8 cyber-card mb-8 animate-fade-in">
            <h1 className="text-4xl md:text-6xl font-black mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-green-400 bg-clip-text text-transparent animate-neon-text">
              <span className="font-mono">&lt;</span>
              AI MODEL
              <span className="font-mono">/&gt;</span>
            </h1>
            <h2 className="text-xl md:text-2xl font-mono text-blue-400/80 mb-2">
              PERFORMANCE DASHBOARD
            </h2>
            <p className="text-gray-400 font-mono text-sm">
              [ REAL-TIME BENCHMARKING SYSTEM v2.0 ]
            </p>
          </div>
          
          <div className="flex justify-center gap-4 flex-wrap animate-slide-up">
            <button
              onClick={addScenario}
              className="cyber-button primary flex items-center gap-2 px-6 py-3 rounded-lg font-medium"
            >
              <Plus size={20} />
              <span className="font-mono">ADD_SCENARIO</span>
            </button>
            <button
              onClick={saveData}
              className="cyber-button success flex items-center gap-2 px-6 py-3 rounded-lg font-medium"
            >
              <Save size={20} />
              <span className="font-mono">SAVE_DATA</span>
            </button>
            <button
              onClick={resetData}
              className="cyber-button danger flex items-center gap-2 px-6 py-3 rounded-lg font-medium"
            >
              <RotateCcw size={20} />
              <span className="font-mono">RESET_ALL</span>
            </button>
          </div>
        </header>
        {/* Scenarios Horizontal Layout */}
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis]}
        >
          <div className="flex flex-wrap justify-center gap-6 max-w-7xl mx-auto">
            {scenarios.map((scenario) => (
              <ScenarioCard
                key={scenario.id}
                scenario={scenario}
                onUpdateScenarioName={updateScenarioName}
                onDeleteScenario={deleteScenario}
                onUpdateModelName={updateModelName}
                onAddModel={addModel}
                onDeleteModel={deleteModel}
                activeId={activeId}
              />
            ))}
          </div>
        </DndContext>
      </div>
    </div>
  );
}

export default App;